#!/usr/bin/env python3
"""
Test script to validate the Hugging Face implementation of evaluate_original.py
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from evaluate_original import HuggingFaceClient, NumericalRange


async def test_basic_functionality():
    """Test basic functionality of the HuggingFaceClient."""
    print("Testing HuggingFaceClient basic functionality...")

    try:
        # Initialize client
        print("1. Initializing HuggingFaceClient...")
        client = HuggingFaceClient()
        print("✓ Client initialized successfully")

        # Test regular chat completion
        print("\n2. Testing regular chat completion...")
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is 2+2? Answer briefly."},
        ]

        completion = await client.chat.completions.create(
            model="qwen2.5-7b-instruct", messages=messages, max_completion_tokens=50
        )

        response = completion.choices[0].message.content
        print(f"✓ Regular completion response: {response[:100]}...")

        # Test structured output
        print("\n3. Testing structured output...")
        numerical_messages = [
            {
                "role": "developer",
                "content": 'You are a helpful assistant. Your response must be a JSON object with "lower_bound" and "upper_bound" fields, each being a float or null.',
            },
            {
                "role": "user",
                "content": "What is the approximate range of human body temperature in Celsius? Provide lower_bound and upper_bound.",
            },
        ]

        structured_completion = await client.beta.chat.completions.parse(
            model="qwen2.5-7b-instruct",
            messages=numerical_messages,
            response_format=NumericalRange,
            reasoning_effort="low",
        )

        parsed_response = structured_completion.choices[0].message.parsed
        print(
            f"✓ Structured output: lower_bound={parsed_response.lower_bound}, upper_bound={parsed_response.upper_bound}"
        )

        print("\n✅ All basic tests passed!")
        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_evaluation_function():
    """Test the evaluation function with sample data."""
    print("\nTesting evaluation function...")

    try:
        from evaluate_original import evaluate_response

        # Test binary evaluation
        print("1. Testing binary evaluation...")

        # Create a simple test case
        system_prompt = "You are a helpful assistant."
        user_prompt = "Is the sky blue?"
        model_response = "Yes, the sky is blue during the day."
        proposition = "The sky is <pivotal>blue/red</pivotal>."

        result = await evaluate_response(
            client=HuggingFaceClient(),  # Create a new client for testing
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_response=model_response,
            proposition=proposition,
            concurrency_limit=1,
        )

        print(f"✓ Binary evaluation result: {result}")

        # Test numerical evaluation
        print("\n2. Testing numerical evaluation...")

        numerical_proposition = (
            "The average human body temperature is <value> degrees Celsius."
        )
        numerical_response = (
            "The average human body temperature is approximately 37 degrees Celsius."
        )

        numerical_result = await evaluate_response(
            client=HuggingFaceClient(),
            system_prompt=system_prompt,
            user_prompt="What is the average human body temperature?",
            model_response=numerical_response,
            proposition=numerical_proposition,
            concurrency_limit=1,
        )

        print(f"✓ Numerical evaluation result: {numerical_result}")

        print("\n✅ Evaluation function tests passed!")
        return True

    except Exception as e:
        print(f"❌ Evaluation test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🧪 Testing Hugging Face Implementation of evaluate_original.py")
    print("=" * 60)

    # Test basic functionality
    basic_test_passed = await test_basic_functionality()

    if basic_test_passed:
        # Test evaluation function
        eval_test_passed = await test_evaluation_function()

        if eval_test_passed:
            print("\n🎉 All tests passed! The implementation is working correctly.")
            print(
                "\nNote: The actual model responses may vary, but the interface is compatible."
            )
        else:
            print("\n⚠️  Basic tests passed but evaluation tests failed.")
            print(
                "The core functionality works, but there may be issues with the evaluation logic."
            )
    else:
        print("\n❌ Basic tests failed. Please check the implementation.")

    print("\n" + "=" * 60)
    print("Test completed.")


if __name__ == "__main__":
    asyncio.run(main())
