#!/usr/bin/env python3
"""
Check if all required dependencies are available for the Hugging Face implementation.
"""

import sys
import importlib


def check_dependency(module_name, package_name=None):
    """Check if a dependency is available."""
    try:
        importlib.import_module(module_name)
        print(f"✓ {package_name or module_name} is available")
        return True
    except ImportError:
        print(f"❌ {package_name or module_name} is NOT available")
        return False


def check_torch_cuda():
    """Check PyTorch and CUDA availability."""
    try:
        import torch

        print(f"✓ PyTorch version: {torch.__version__}")

        if torch.cuda.is_available():
            num_gpus = torch.cuda.device_count()
            print(f"✓ CUDA is available with {num_gpus} GPU(s)")
            for i in range(num_gpus):
                gpu_name = torch.cuda.get_device_name(i)
                memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"  - GPU {i}: {gpu_name} ({memory:.1f} GB)")
        else:
            print("⚠️  CUDA is not available - will use CPU")

        return True
    except ImportError:
        print("❌ PyTorch is not available")
        return False


def main():
    """Main dependency check function."""
    print("🔍 Checking dependencies for Hugging Face implementation")
    print("=" * 60)

    dependencies = [
        ("pandas", "pandas"),
        ("transformers", "transformers"),
        ("pydantic", "pydantic"),
        ("tqdm", "tqdm"),
        ("asyncio", "asyncio (built-in)"),
        ("json", "json (built-in)"),
        ("re", "re (built-in)"),
        ("os", "os (built-in)"),
        ("glob", "glob (built-in)"),
    ]

    all_available = True

    # Check basic dependencies
    print("\n📦 Checking Python packages:")
    for module, package in dependencies:
        if not check_dependency(module, package):
            all_available = False

    # Check PyTorch and CUDA
    print("\n🔥 Checking PyTorch and CUDA:")
    if not check_torch_cuda():
        all_available = False

    # Check if the model can be loaded (optional)
    print("\n🤖 Checking model availability:")
    try:
        from transformers import AutoTokenizer

        model_name = "Qwen/Qwen2.5-7B-Instruct"
        print(f"Checking if {model_name} can be loaded...")

        # Just check if we can load the tokenizer config (doesn't download the full model)
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        print(f"✓ Model {model_name} is accessible")
    except Exception as e:
        print(f"⚠️  Could not verify model access: {e}")
        print("   This might be due to network issues or the model not being cached.")
        print("   The model will be downloaded on first use.")

    print("\n" + "=" * 60)

    if all_available:
        print("🎉 All required dependencies are available!")
        print(
            "\nYou can now run the modified evaluate_original.py with Hugging Face models."
        )
        print("\nTo test the implementation, run:")
        print("  python test_hf_implementation.py")
    else:
        print("❌ Some dependencies are missing.")
        print("\nTo install missing dependencies, run:")
        print("  pip install torch transformers pandas pydantic tqdm")
        print("\nFor CUDA support, install PyTorch with CUDA:")
        print("  pip install torch --index-url https://download.pytorch.org/whl/cu118")

    return all_available


if __name__ == "__main__":
    main()
